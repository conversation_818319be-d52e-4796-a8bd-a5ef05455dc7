---
# Process single file - handles string replacement with idempotency
# This file is included for each file. It expects 'current_file', 'current_ip', 'mongo_old_string', 'mongo_new_string' from parent.

- name: "File: Check if file exists: {{ current_ip }}:{{ current_file }}" # noqa: name[template] # noqa: name[template]
  block:
    # Try with sudo first if sudo is available
    - name: "File: Check file existence with sudo for {{ current_ip }}:{{ current_file }}"
      ansible.builtin.shell: "sudo test -f '{{ current_file }}' && echo 'EXISTS' || echo 'NOT_EXISTS'"
      register: file_check_sudo
      failed_when: false
      changed_when: false
      delegate_to: "{{ current_ip }}"
      when: service_sudo | default(false)

    # Try without sudo if sudo failed or not available
    - name: "File: Check file existence without sudo for {{ current_ip }}:{{ current_file }}"
      ansible.builtin.stat:
        path: "{{ current_file }}"
      register: file_stat_nosudo
      failed_when: false
      delegate_to: "{{ current_ip }}"
      when: >
        (not (service_sudo | default(false))) or
        (service_sudo | default(false) and
         (file_check_sudo is not defined or file_check_sudo.rc != 0))

    # Set final file existence result
    - name: "File: Set final file existence result for {{ current_ip }}:{{ current_file }}"
      ansible.builtin.set_fact:
        file_stat:
          stat:
            exists: "{{ (file_check_sudo is defined and file_check_sudo.stdout == 'EXISTS') or (file_stat_nosudo is defined and file_stat_nosudo.stat.exists | default(false)) }}"

  rescue:
    - name: "File: Handle file check failure for {{ current_ip }}:{{ current_file }}"
      ansible.builtin.set_fact:
        file_stat:
          stat:
            exists: false

- name: "File: Skip processing if file does not exist: {{ current_ip }}:{{ current_file }}" # noqa: name[template] # noqa: name[template]
  ansible.builtin.debug:
    msg: "Warning: File {{ current_file }} does not exist on {{ current_ip }}. Skipping this file."
  when: not file_stat.stat.exists | default(false)

- name: "File: Check sudo permissions for file operations on {{ current_ip }}" # noqa: name[template]
  ansible.builtin.command: "sudo -n true" # noqa: command-instead-of-shell
  register: file_sudo_check_result
  failed_when: false
  changed_when: false
  delegate_to: "{{ current_ip }}"
  when:
    - file_stat.stat.exists | default(false)
    - service_sudo | default(false)
    - file_check_sudo is not defined or file_check_sudo.rc != 0

- name: "File: Process file replacement: {{ current_ip }}:{{ current_file }}" # noqa: name[template]
  when: file_stat.stat.exists | default(false)
  block:
    - name: "File: Check if old string exists: {{ current_ip }}:{{ current_file }}" # noqa: name[template]
      ansible.builtin.shell: |
        if [ -f "{{ current_file }}" ]; then
          if grep -F "{{ mongo_old_string }}" "{{ current_file }}" >/dev/null 2>&1; then
            echo "FOUND"
          else
            echo "NOT_FOUND"
          fi
        else
          echo "FILE_NOT_EXISTS"
        fi
      register: string_check_result
      delegate_to: "{{ current_ip }}"
      changed_when: false
      failed_when: false

    - name: "File: Create backup before replacement: {{ current_ip }}:{{ current_file }}" # noqa: name[template]
      when: string_check_result.stdout == "FOUND"
      block:
        # Try backup with sudo first if sudo is required and available
        - name: "File: Create backup with sudo for {{ current_ip }}:{{ current_file }}" # noqa: name[template]
          ansible.builtin.shell: "sudo cp '{{ current_file }}' '{{ current_file }}.backup.{{ ansible_date_time.epoch }}'"
          register: backup_result_sudo
          failed_when: false
          changed_when: backup_result_sudo.rc == 0
          delegate_to: "{{ current_ip }}"
          when:
            - service_sudo | default(false)
            - >
              (file_check_sudo is defined and file_check_sudo.stdout == 'EXISTS') or
              (file_sudo_check_result is defined and
               file_sudo_check_result.rc == 0)

        # Try backup without sudo if sudo failed or not available
        - name: "File: Create backup without sudo for {{ current_ip }}:{{ current_file }}" # noqa: name[template]
          ansible.builtin.copy:
            src: "{{ current_file }}"
            dest: "{{ current_file }}.backup.{{ ansible_date_time.epoch }}"
            remote_src: true
            backup: false
            mode: "0644"
          register: backup_result_nosudo
          failed_when: false
          delegate_to: "{{ current_ip }}"
          when: >
            (not (service_sudo | default(false))) or
            (service_sudo | default(false) and
             (file_sudo_check_result is not defined or file_sudo_check_result.rc != 0 or
              (backup_result_sudo is defined and backup_result_sudo.rc != 0)))

      rescue:
        - name: "File: Handle backup failure for {{ current_ip }}:{{ current_file }}"
          ansible.builtin.debug:
            msg: |
              Backup creation failed for {{ current_ip }}:{{ current_file }}, but continuing.
              This may indicate permission issues.

    - name: "File: Perform string replacement: {{ current_ip }}:{{ current_file }}" # noqa: name[template]
      when: string_check_result.stdout == "FOUND"
      block:
        # Try replacement with sudo first if sudo is required and available
        - name: "File: Perform string replacement with sudo for {{ current_ip }}:{{ current_file }}" # noqa: name[template]
          ansible.builtin.shell: |
            sudo sed -i.backup.{{ ansible_date_time.epoch }} \
              's/{{ mongo_old_string | regex_escape }}/{{ mongo_new_string | regex_escape }}/g' \
              '{{ current_file }}'
          register: replacement_result_sudo
          failed_when: false
          changed_when: replacement_result_sudo.rc == 0
          delegate_to: "{{ current_ip }}"
          when:
            - service_sudo | default(false)
            - >
              (file_check_sudo is defined and file_check_sudo.stdout == 'EXISTS') or
              (file_sudo_check_result is defined and
               file_sudo_check_result.rc == 0)

        # Try replacement without sudo if sudo failed or not available
        - name: "File: Perform string replacement without sudo for {{ current_ip }}:{{ current_file }}" # noqa: name[template]
          ansible.builtin.replace:
            path: "{{ current_file }}"
            regexp: "{{ mongo_old_string | regex_escape }}"
            replace: "{{ mongo_new_string }}"
            backup: true
          register: replacement_result_nosudo
          failed_when: false
          delegate_to: "{{ current_ip }}"
          when: >
            (not (service_sudo | default(false))) or
            (service_sudo | default(false) and
             (file_sudo_check_result is not defined or file_sudo_check_result.rc != 0 or
              (replacement_result_sudo is defined and replacement_result_sudo.rc != 0)))

        # Set final replacement result
        - name: "File: Set final replacement result for {{ current_ip }}:{{ current_file }}" # noqa: name[template]
          ansible.builtin.set_fact:
            replacement_result: "{{ replacement_result_sudo if (replacement_result_sudo is defined and replacement_result_sudo.rc == 0) else replacement_result_nosudo }}"

      rescue:
        - name: "File: Handle replacement failure for {{ current_ip }}:{{ current_file }}" # noqa: name[template]
          ansible.builtin.debug:
            msg: |
              String replacement failed for {{ current_ip }}:{{ current_file }}, but continuing.
              This may indicate permission issues or file corruption.

        - name: "File: Set failed replacement result for {{ current_ip }}:{{ current_file }}" # noqa: name[template]
          ansible.builtin.set_fact:
            replacement_result:
              changed: false
              failed: true

    - name: "File: Verify replacement result: {{ current_ip }}:{{ current_file }}" # noqa: name[template]
      ansible.builtin.shell: |
        if [ -f "{{ current_file }}" ]; then
          if grep -F "{{ mongo_new_string }}" "{{ current_file }}" >/dev/null 2>&1; then
            echo "REPLACEMENT_SUCCESS"
          else
            echo "REPLACEMENT_FAILED"
          fi
        else
          echo "FILE_NOT_EXISTS"
        fi
      register: verification_result
      delegate_to: "{{ current_ip }}"
      changed_when: false
      failed_when: false
      when: string_check_result.stdout == "FOUND"

    - name: "File: Display replacement result: {{ current_ip }}:{{ current_file }}" # noqa: name[template]
      ansible.builtin.debug:
        msg: |
          File Processing Result: {{ current_ip }}:{{ current_file }}
          =======================================================
          File exists: {{ file_stat.stat.exists | default(false) }}
          Old string found: {{ 'Yes' if string_check_result.stdout == 'FOUND' else 'No' }}
          {% if string_check_result.stdout == "FOUND" %}
          Replacement performed: {{ 'Yes' if replacement_result.changed else 'No' }}
          Verification status: {{ verification_result.stdout }}
          {% if replacement_result.changed %}
          Backup created: {{ replacement_result.backup_file | default('N/A') }}
          {% endif %}
          {% else %}
          Action taken: No replacement needed (old string not found)
          {% endif %}
          Old string: {{ mongo_old_string }}
          New string: {{ mongo_new_string }}

    - name: "File: Report string not found: {{ current_ip }}:{{ current_file }}" # noqa: name[template]
      ansible.builtin.debug:
        msg: |
          No Replacement Needed: {{ current_ip }}:{{ current_file }}
          ================================================
          Reason: Old string "{{ mongo_old_string }}" was not found in the file.
          This is normal if the replacement was already performed previously.
          File content check status: {{ string_check_result.stdout }}
      when: string_check_result.stdout != "FOUND"

    - name: "File: Handle replacement failure: {{ current_ip }}:{{ current_file }}" # noqa: name[template]
      ansible.builtin.debug:
        msg: |
          WARNING: Replacement verification failed for {{ current_ip }}:{{ current_file }}
          This might indicate a partial replacement or file corruption.
          Please verify the file content manually.
          Expected new string: {{ mongo_new_string }}
          Verification result: {{ verification_result.stdout }}
      when:
        - string_check_result.stdout == "FOUND"
        - verification_result.stdout is defined
        - verification_result.stdout != "REPLACEMENT_SUCCESS"
